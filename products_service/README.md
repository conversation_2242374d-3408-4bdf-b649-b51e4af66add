# Products Service

A completely self-contained microservice for managing products in the Fehadan Meat Processing system.

## Features

- ✅ **Product Management**: Full CRUD operations for products
- ✅ **Category Management**: Automatic category creation and multi-category support
- ✅ **Event Publishing**: Kafka-based event system for microservice communication
- ✅ **Self-Contained Infrastructure**: Includes PostgreSQL, Redis, Kafka, and Zookeeper
- ✅ **Independent Deployment**: Runs completely standalone without external dependencies
- ✅ **RESTful API**: FastAPI with automatic OpenAPI documentation
- ✅ **JWT Authentication**: Integration with external authentication service
- ✅ **Health Monitoring**: Built-in health checks and monitoring endpoints

## Quick Start

### Prerequisites

- Docker and Docker Compose
- Git

### Self-Contained Deployment (Recommended)

The service includes its own complete infrastructure stack and runs independently:

1. **Navigate to the service directory:**

   ```bash
   cd products_service
   ```

2. **Start the complete service stack:**

   ```bash
   docker-compose up -d
   ```

3. **Verify the service is running:**
   ```bash
   curl http://localhost:8002/health
   ```

### Service Endpoints

When running with docker-compose, the following services are available:

- **Products API**: http://localhost:8002
- **PostgreSQL Database**: localhost:5433
- **Redis Cache**: localhost:6380
- **Kafka Message Broker**: localhost:9093
- **API Documentation**: http://localhost:8002/docs

### Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Products Service                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  Products API   │  │   PostgreSQL    │  │     Redis       │ │
│  │   (Port 8002)   │  │   (Port 5433)   │  │   (Port 6380)   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐                     │
│  │     Kafka       │  │   Zookeeper     │                     │
│  │   (Port 9093)   │  │   (Port 2181)   │                     │
│  └─────────────────┘  └─────────────────┘                     │
└─────────────────────────────────────────────────────────────────┘
```

## API Documentation

Once running, visit:

- **Swagger UI:** http://localhost:8002/docs
- **ReDoc:** http://localhost:8002/redoc

## Configuration

The service can be configured through environment variables. See `.env.example` for all available options.

### Key Configuration Options

- `DATABASE_URL`: Database connection string
- `JWT_SECRET_KEY`: Secret key for JWT token validation
- `KAFKA_BOOTSTRAP_SERVERS`: Kafka broker addresses (optional)
- `DEBUG`: Enable debug mode

## Architecture

The service follows a clean architecture pattern:

```
products_service/
├── app/
│   ├── api/           # API endpoints
│   ├── core/          # Core configuration and utilities
│   ├── events/        # Event handling (Kafka)
│   ├── models.py      # Database models
│   ├── repositories/  # Data access layer
│   ├── schemas.py     # Pydantic schemas
│   ├── services/      # Business logic
│   └── utils/         # Utility functions
├── tests/             # Test suite
├── main.py           # Application entry point
└── requirements.txt  # Python dependencies
```

## Testing

Run the test suite:

```bash
pytest
```

## Health Check

The service provides a health check endpoint:

```bash
curl http://localhost:8002/health
```

## Integration

This service can run independently or as part of the larger Fehadan Meat Processing system. It communicates with other services through:

- **REST APIs** for synchronous communication
- **Kafka events** for asynchronous communication (optional)

## Development

### Adding New Features

1. Add models in `app/models.py`
2. Create schemas in `app/schemas.py`
3. Implement repository methods in `app/repositories/`
4. Add business logic in `app/services/`
5. Create API endpoints in `app/api/`
6. Write tests in `tests/`

### Code Style

The project follows Python best practices:

- Type hints
- Docstrings
- Clean architecture principles
- Dependency injection

## License

This project is part of the Fehadan Meat Processing system.
