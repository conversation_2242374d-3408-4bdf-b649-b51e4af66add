"""
Product Service for Products Service.

This module handles all product operations following the fehdan-inventory
service patterns including business logic, validation, and event publishing.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from decimal import Decimal
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import LoggingService
from app.repositories.product import ProductRepository
from app.repositories.category import CategoryRepository
from app.schemas import (
    ProductCreate,
    ProductResponse,
    ProductUpdate,
    ProductAvailabilityResponse,
)
from app.utils.validation import ValidationUtils
from app.events.publisher import event_publisher


class ProductService:
    """
    Service layer for product management operations.

    This service handles business logic for product operations including:
    - Product creation, update, deletion
    - Category management integration
    - Event publishing for other services
    - Product search and filtering
    - Business rule validation

    The service follows the Single Responsibility Principle by focusing on
    business logic while delegating data access to repository layers.
    """

    def __init__(self, db: AsyncSession):
        """
        Initialize ProductService with required dependencies.

        Args:
            db (AsyncSession): Database session for repository operations
        """
        self.product_repo = ProductRepository(db)
        self.category_repo = CategoryRepository(db)
        self.validation_utils = ValidationUtils(db)
        self.logger = LoggingService()
        self.db = db

    async def create_product(
        self, current_user: Dict[str, Any], product_data: ProductCreate
    ) -> ProductResponse:
        """
        Create a new product and publish event for inventory service.

        Args:
            current_user: The user creating the product
            product_data (ProductCreate): Data for the new product

        Returns:
            ProductResponse: The created product with all relationships

        Raises:
            HTTPException: If validation fails or creation errors occur
        """
        try:
            self.logger.log(
                f"Creating product: {product_data.name}",
                level="info",
                app_name="products",
            )

            # Validate product creation business rules
            await self.validation_utils.validate_product_creation(product_data)

            # Process categories - get or create them
            categories = []
            if product_data.categories:
                categories = await self.category_repo.get_or_create_categories(product_data.categories)
                self.logger.log(
                    f"Processed {len(categories)} categories for product: {product_data.name}",
                    level="info",
                    app_name="products",
                )

            # Create the product with categories (repository will flush but not commit)
            product = await self.product_repo.create_product(product_data, categories)

            # Publish product created event before committing
            await event_publisher.publish_product_created(
                product_id=product.id,
                product_name=product.name,
                price=float(product.price),
                currency=product.currency,
                stock_quantity=product.stock_quantity or 0,
                created_by=current_user.get("email", "system"),
                categories=[cat.name for cat in product.categories],
            )

            # Commit the transaction only after successful event publishing
            await self.db.commit()

            self.logger.log(
                f"Product created successfully: {product.name} (ID: {product.id})",
                level="info",
                app_name="products",
            )

            return product

        except HTTPException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                f"Error creating product: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create product: {str(e)}",
            )

    async def get_product_by_id(self, product_id: int) -> ProductResponse:
        """
        Retrieve a product by ID.

        Args:
            product_id (int): ID of the product to retrieve

        Returns:
            ProductResponse: The product data

        Raises:
            HTTPException: If product not found or retrieval fails
        """
        try:
            self.logger.log(
                f"Retrieving product with ID: {product_id}",
                level="info",
                app_name="products",
            )

            product = await self.product_repo.get_product_by_id(product_id)
            return product

        except HTTPException:
            raise
        except Exception as e:
            self.logger.log(
                f"Error retrieving product {product_id}: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to retrieve product: {str(e)}",
            )

    async def list_products(
        self,
        category: Optional[str] = None,
        in_stock: Optional[bool] = None,
        search: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[ProductResponse]:
        """
        List products with optional filtering and pagination.
        """
        try:
            self.logger.log(
                f"Listing products with filters: category={category}, in_stock={in_stock}, search={search}",
                level="info",
                app_name="products",
            )

            products = await self.product_repo.list_products(
                category=category,
                in_stock=in_stock,
                search=search,
                skip=offset,
                limit=limit,
            )

            return products

        except Exception as e:
            self.logger.log(
                f"Error listing products: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to list products: {str(e)}",
            )

    async def update_product(
        self, product_id: int, product_data: ProductUpdate, current_user: Dict[str, Any]
    ) -> ProductResponse:
        """Update an existing product."""
        try:
            self.logger.log(
                f"Updating product with ID: {product_id}",
                level="info",
                app_name="products",
            )

            # Validate product update business rules
            await self.validation_utils.validate_product_update(product_id, product_data)

            # Process categories if being updated
            categories = None
            if product_data.categories is not None:
                categories = await self._process_categories(product_data.categories)

            # Get original product for comparison
            original_product = await self.product_repo.get_product_by_id(product_id)

            # Update the product (repository will flush but not commit)
            updated_product = await self.product_repo.update_product(product_id, product_data, categories)

            # Determine what fields were updated
            try:
                print(f"DEBUG: Calling _get_updated_fields with product_data type: {type(product_data)}")
                updated_fields = self._get_updated_fields(original_product, product_data)
                print(f"DEBUG: _get_updated_fields completed successfully")
            except Exception as e:
                print(f"DEBUG: Error in _get_updated_fields: {e}")
                raise

            # Publish product updated event before committing
            if updated_fields:
                # Debug: Print updated_fields to see what's causing the serialization issue
                print(f"DEBUG: About to publish event with updated_fields: {updated_fields}")
                print(f"DEBUG: Type of updated_fields: {type(updated_fields)}")
                for key, value in updated_fields.items():
                    print(f"DEBUG: {key}: {value} (type: {type(value)})")
                    if isinstance(value, dict):
                        for sub_key, sub_value in value.items():
                            print(f"DEBUG:   {sub_key}: {sub_value} (type: {type(sub_value)})")

                # Publish product updated event
                await event_publisher.publish_product_updated(
                    product_id=product_id,
                    product_name=updated_product.name,
                    updated_fields=updated_fields,
                    updated_by=current_user.get("email", "system"),
                )

            # Commit the transaction only after successful event publishing
            await self.db.commit()

            self.logger.log(
                f"Product updated successfully: {updated_product.name} (ID: {product_id})",
                level="info",
                app_name="products",
            )

            return updated_product

        except HTTPException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                f"Error updating product {product_id}: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update product: {str(e)}",
            )

    async def delete_product(self, product_id: int, current_user: Dict[str, Any]) -> None:
        """Delete a product."""
        try:
            self.logger.log(
                f"Deleting product with ID: {product_id}",
                level="info",
                app_name="products",
            )

            # Get product before deletion for event publishing
            product = await self.product_repo.get_product_by_id(product_id)

            # Delete the product (repository will flush but not commit)
            await self.product_repo.delete_product(product_id)

            # Publish product deleted event before committing
            await event_publisher.publish_product_deleted(
                product_id=product_id,
                product_name=product.name,
                deleted_by=current_user.get("email", "system"),
            )

            # Commit the transaction only after successful event publishing
            await self.db.commit()

            self.logger.log(
                f"Product deleted successfully: {product.name} (ID: {product_id})",
                level="info",
                app_name="products",
            )

        except HTTPException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                f"Error deleting product {product_id}: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete product: {str(e)}",
            )

    async def check_availability(
        self, product_id: int, quantity: int = 1
    ) -> ProductAvailabilityResponse:
        """Check product availability."""
        try:
            product = await self.product_repo.get_product_by_id(product_id)

            available = product.in_stock and (product.stock_quantity or 0) >= quantity
            message = "Available" if available else "Not available"

            if not product.in_stock:
                message = "Product is out of stock"
            elif (product.stock_quantity or 0) < quantity:
                message = f"Only {product.stock_quantity} units available"

            return ProductAvailabilityResponse(
                product_id=product_id,
                available=available,
                stock_quantity=product.stock_quantity or 0,
                requested_quantity=quantity,
                message=message,
            )

        except HTTPException:
            raise
        except Exception as e:
            self.logger.log(
                f"Error checking availability for product {product_id}: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to check availability: {str(e)}",
            )

    async def advanced_search(
        self,
        name: Optional[str] = None,
        description: Optional[str] = None,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None,
        categories: Optional[List[str]] = None,
        tags: Optional[List[str]] = None,
        in_stock: Optional[bool] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[ProductResponse]:
        """Advanced product search with multiple criteria."""
        try:
            self.logger.log(
                f"Advanced search with criteria: name={name}, categories={categories}",
                level="info",
                app_name="products",
            )

            products = await self.product_repo.advanced_search(
                name=name,
                description=description,
                min_price=min_price,
                max_price=max_price,
                categories=categories,
                tags=tags,
                in_stock=in_stock,
                skip=offset,
                limit=limit,
            )

            return products

        except Exception as e:
            self.logger.log(
                f"Error in advanced search: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to perform advanced search: {str(e)}",
            )



    def _get_updated_fields(
        self, original_product: ProductResponse, update_data: ProductUpdate
    ) -> Dict[str, Any]:
        """Determine which fields were updated."""
        print(f"DEBUG: _get_updated_fields called with update_data type: {type(update_data)}")
        updated_fields = {}
        update_dict = update_data.model_dump(exclude_unset=True, exclude_none=True)
        print(f"DEBUG: update_dict: {update_dict}")

        for field, new_value in update_dict.items():
            # Special handling for categories (list of objects)
            if field == "categories":
                # Convert category objects to simple names for comparison
                original_categories = getattr(original_product, field, None)
                if original_categories:
                    original_names = [cat.name if hasattr(cat, 'name') else str(cat) for cat in original_categories]
                else:
                    original_names = []

                new_names = new_value if isinstance(new_value, list) else []

                if set(original_names) != set(new_names):
                    updated_fields[field] = {
                        "old_value": original_names,
                        "new_value": new_names,
                    }
                continue

            # Handle other fields
            original_value = getattr(original_product, field, None)
            if original_value != new_value:
                # Convert Decimal values to float for JSON serialization
                if isinstance(original_value, Decimal):
                    original_value = float(original_value)
                if isinstance(new_value, Decimal):
                    new_value = float(new_value)

                # Convert datetime values to ISO format strings for JSON serialization
                if isinstance(original_value, datetime):
                    original_value = original_value.isoformat()
                if isinstance(new_value, datetime):
                    new_value = new_value.isoformat()

                # Ensure complex objects are converted to serializable format
                if hasattr(original_value, 'model_dump'):
                    original_value = original_value.model_dump()
                elif hasattr(original_value, '__dict__'):
                    original_value = str(original_value)

                if hasattr(new_value, 'model_dump'):
                    new_value = new_value.model_dump()
                elif hasattr(new_value, '__dict__') and not isinstance(new_value, (str, int, float, bool, list, dict)):
                    new_value = str(new_value)

                updated_fields[field] = {
                    "old_value": original_value,
                    "new_value": new_value,
                }

        # Debug: Check for non-serializable objects in updated_fields
        for field, change in updated_fields.items():
            if hasattr(change.get('old_value'), 'model_dump'):
                self.logger.log(f"Non-serializable old_value in {field}: {type(change['old_value'])}", level="error", app_name="products")
            if hasattr(change.get('new_value'), 'model_dump'):
                self.logger.log(f"Non-serializable new_value in {field}: {type(change['new_value'])}", level="error", app_name="products")

        return updated_fields
    
    async def _process_categories(self, category_names: List[str]) -> List:
        """Get or create categories by names."""
        if not category_names:
            return []
        categories = await self.category_repo.get_or_create_categories(category_names)
        return categories