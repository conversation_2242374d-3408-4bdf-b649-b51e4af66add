"""
Authentication service for Products Service.

This module provides JWT verification and user authentication following
the fehdan-inventory service patterns.
"""

from fastapi import Header, HTTPException, Depends
from jose import jwt, JWTError
from jose.exceptions import J<PERSON>Error, ExpiredSignatureError
import httpx
import logging
from typing import Dict, Any
from app.core.config import settings
from app.schemas import CurrentUserResponse

# Set up logger
logger = logging.getLogger(__name__)




# Cache for public key
_public_key_cache = None


async def get_public_key():
    """Fetch and cache the public key from auth service JWKS endpoint."""
    global _public_key_cache
    if _public_key_cache is None:
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(
                    f"{settings.AUTH_SERVICE_URL}/auth/.well-known/jwks.json"
                )
                if response.status_code == 200:
                    jwks_data = response.json()
                    _public_key_cache = jwks_data.get("public_key")
                    if not _public_key_cache:
                        logger.error("No public_key found in JWKS response")
                        raise ValueError("No public_key found in JWKS response")
                    logger.info("Public key successfully fetched from authentication service")
                else:
                    logger.error(f"Failed to fetch JWKS: HTTP {response.status_code}")
                    raise ValueError(f"Failed to fetch JWKS: {response.status_code}")

        except httpx.TimeoutException as e:
            logger.error(f"Timeout while fetching public key: {e}")
            # Fallback to config if JWKS fetch fails
            if settings.PUBLIC_KEY:
                logger.warning("Using fallback public key from configuration")
                _public_key_cache = settings.PUBLIC_KEY
            else:
                raise ValueError(f"Could not fetch public key due to timeout: {e}")

        except httpx.RequestError as e:
            logger.error(f"Network error while fetching public key: {e}")
            # Fallback to config if JWKS fetch fails
            if settings.PUBLIC_KEY:
                logger.warning("Using fallback public key from configuration")
                _public_key_cache = settings.PUBLIC_KEY
            else:
                raise ValueError(f"Could not fetch public key due to network error: {e}")

        except Exception as e:
            logger.error(f"Unexpected error while fetching public key: {e}")
            # Fallback to config if JWKS fetch fails
            if settings.PUBLIC_KEY:
                logger.warning("Using fallback public key from configuration")
                _public_key_cache = settings.PUBLIC_KEY
            else:
                raise ValueError(f"Could not fetch public key: {e}")
    return _public_key_cache


async def get_current_user(
    authorization: str = Header(None), verify_token: bool = False
) -> CurrentUserResponse:
    """
    Get current authenticated user from JWT token.
    
    Args:
        authorization: Authorization header containing Bearer token
        verify_token: Whether to verify token with auth service
        
    Returns:
        CurrentUserResponse: User data from token or auth service
        
    Raises:
        HTTPException: If authentication fails
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Unauthorized")
    token = authorization.replace("Bearer ", "")

    if verify_token:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{settings.AUTH_SERVICE_URL}/auth/verify-token",
                headers={"Authorization": f"Bearer {token}"},
            )
            if response.status_code != 200:
                raise HTTPException(status_code=401, detail="Token invalid or revoked")
            return response.json()

    # Local JWT verification
    try:
        public_key = await get_public_key()

        payload = jwt.decode(
            token,
            public_key,
            algorithms=[settings.JWT_ALGORITHM],
            options={
                "require_exp": True
            },  # Remove issuer requirement since token doesn't have it
        )
        return payload

    except ExpiredSignatureError as e:
        logger.warning(f"JWT token expired: {e}")
        raise HTTPException(status_code=401, detail="Token expired")

    except JWSError as e:
        logger.warning(f"Invalid JWT token format: {e}")
        raise HTTPException(status_code=401, detail="Invalid token format")

    except JWTError as e:
        logger.warning(f"JWT verification error: {e}")
        raise HTTPException(status_code=401, detail="Token verification failed")

    except Exception as e:
        logger.error(f"Unexpected error during JWT verification: {e}")
        raise HTTPException(status_code=500, detail="Authentication service error")


async def get_current_user_verified(
    authorization: str = Header(None),
) -> CurrentUserResponse:
    """Dependency for critical operations that requires token introspection."""
    return await get_current_user(authorization, verify_token=True)


async def require_write_access(
    user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Dependency that requires write access (admin role only for now).

    Args:
        user: Current authenticated user data

    Returns:
        Dict: Authenticated user data with write access

    Raises:
        HTTPException: If user doesn't have write access
    """
    # For now, anyone has permission to write
    # We will adjust this logic based on our requirements
    return user


async def require_read_access(
    user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Dependency that requires read access (any authenticated user).

    Args:
        user: Current authenticated user data

    Returns:
        Dict: Authenticated user data

    Raises:
        HTTPException: If user is not authenticated
    """
    return user
