"""
Event publisher for Products Service.

This module provides event publishing capabilities following the
fehdan-inventory service patterns.
"""

import json
import logging
from datetime import datetime
from decimal import Decimal
from typing import Union, Optional, Dict, Any
from confluent_kafka import Producer
from app.events.kafka_config import create_producer, TOPICS
from app.events.schemas import (
    BaseEvent,
    ProductCreatedEvent,
    ProductUpdatedEvent,
    ProductDeletedEvent,
    CategoryCreatedEvent,
)
from app.core.logging import LoggingService

logger = logging.getLogger(__name__)


class DateTimeEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle datetime and Decimal objects."""

    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        return super().default(obj)


class EventPublisher:
    """
    Event publisher for Products Service.
    
    Handles publishing events to Kafka topics following structured patterns.
    """
    
    def __init__(self):
        self.producer = None
        self.products_topic = TOPICS['products']
        self.logger = LoggingService()
    
    def _get_producer(self) -> Producer:
        """Get or create Kafka producer instance."""
        if self.producer is None:
            self.producer = create_producer()
        return self.producer
    
    def _delivery_callback(self, err, msg):
        """Callback for message delivery confirmation."""
        if err is not None:
            self.logger.log(
                f"Message delivery failed: {err}",
                level="error",
                app_name="products"
            )
        else:
            self.logger.log(
                f"Message delivered to {msg.topic()} [{msg.partition()}] at offset {msg.offset()}",
                level="info",
                app_name="products"
            )
    
    async def publish_event(
        self, event: Union[BaseEvent, dict], topic: str = None, key: str = None
    ):
        """Publish an event to the specified topic or default products topic."""
        try:
            topic = topic or self.products_topic
            producer = self._get_producer()

            # Convert Pydantic model to dict if needed
            print(f"DEBUG: publish_event called with event type: {type(event)}")
            if hasattr(event, "model_dump"):
                print(f"DEBUG: Calling model_dump on event")
                event_data = event.model_dump()
                print(f"DEBUG: model_dump successful, event_data type: {type(event_data)}")
                print(f"DEBUG: Event data: {event_data}")
            elif isinstance(event, dict):
                event_data = event
                print(f"DEBUG: Event data (dict): {event_data}")
            else:
                event_data = event.__dict__
                print(f"DEBUG: Event data (__dict__): {event_data}")

            # Debug: Check for non-serializable objects in event_data
            # self._validate_serializable(event_data)

            # Serialize event data to JSON with custom encoder for datetime/Decimal handling
            try:
                message_value = json.dumps(event_data, cls=DateTimeEncoder)
                print(f"DEBUG: JSON serialized event data: {message_value}")
            except TypeError as e:
                # Debug: Print the event_data to see what's causing the issue
                self.logger.log(f"JSON serialization error: {str(e)}", level="error", app_name="products")
                self.logger.log(f"Event data: {event_data}", level="error", app_name="products")
                raise
            
            # Use event_id as key if no key provided
            message_key = key or event_data.get("event_id", "default")
            print(f"DEBUG: Message key: {message_key}")
            
            # Produce message to Kafka
            producer.produce(
                topic=topic,
                key=message_key,
                value=message_value,
                callback=self._delivery_callback,
            )
            
            # Flush to ensure message is sent
            producer.flush(timeout=10)
            
            self.logger.log(
                f"Published event {event_data.get('event_type')} to topic {topic}",
                level="info",
                app_name="products"
            )
            
        except Exception as e:
            self.logger.log(
                f"Failed to publish event: {str(e)}",
                level="error",
                exception=e,
                app_name="products"
            )
            raise

    def _validate_serializable(self, data, path=""):
        """Recursively validate that data is JSON serializable."""
        if isinstance(data, dict):
            for key, value in data.items():
                self._validate_serializable(value, f"{path}.{key}")
        elif isinstance(data, list):
            for i, item in enumerate(data):
                self._validate_serializable(item, f"{path}[{i}]")
        elif hasattr(data, 'model_dump'):
            # This is a Pydantic model that wasn't converted
            raise ValueError(f"Pydantic model found at {path}: {type(data)}")
        elif hasattr(data, '__dict__') and not isinstance(data, (str, int, float, bool, type(None))):
            # This is a complex object that might not be serializable
            raise ValueError(f"Complex object found at {path}: {type(data)}")

    async def publish_product_created(
        self,
        product_id: int,
        product_name: str,
        price: float,
        currency: str = "USD",
        stock_quantity: int = 0,
        created_by: str = "system",
        categories: Optional[list] = None,
    ):
        """Publish PRODUCT_CREATED event."""
        event = ProductCreatedEvent.create(
            product_id=product_id,
            product_name=product_name,
            price=price,
            currency=currency,
            stock_quantity=stock_quantity,
            created_by=created_by,
            categories=categories,
        )
        await self.publish_event(event)
    
    async def publish_product_updated(
        self,
        product_id: int,
        product_name: str,
        updated_fields: dict,
        updated_by: str = "system",
    ):
        """Publish PRODUCT_UPDATED event."""
        # Debug: Check the type of updated_fields
        print(f"DEBUG: updated_fields type: {type(updated_fields)}")
        print(f"DEBUG: updated_fields content: {updated_fields}")

        event = ProductUpdatedEvent.create(
            product_id=product_id,
            product_name=product_name,
            updated_fields=updated_fields,
            updated_by=updated_by,
        )
        await self.publish_event(event)
    
    async def publish_product_deleted(
        self,
        product_id: int,
        product_name: str,
        deleted_by: str = "system",
    ):
        """Publish PRODUCT_DELETED event."""
        event = ProductDeletedEvent.create(
            product_id=product_id,
            product_name=product_name,
            deleted_by=deleted_by,
        )
        await self.publish_event(event)
    
    async def publish_category_created(
        self,
        category_id: int,
        category_name: str,
        description: Optional[str] = None,
        created_by: str = "system",
    ):
        """Publish CATEGORY_CREATED event."""
        event = CategoryCreatedEvent.create(
            category_id=category_id,
            category_name=category_name,
            description=description,
            created_by=created_by,
        )
        await self.publish_event(event)
    
    def close(self):
        """Close the producer and clean up resources."""
        if self.producer:
            self.producer.flush()
            self.producer = None


# Global event publisher instance
event_publisher = EventPublisher()
