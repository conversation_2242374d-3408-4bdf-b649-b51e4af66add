"""
Configuration settings for the Products Service.

This module provides environment-based configuration management following
the fehdan-inventory service patterns.
"""

import os
from typing import List, Optional
from pydantic import ConfigDict
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    model_config = ConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=True, extra="allow"
    )

    # Environment Configuration
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "production")

    # Database Configuration
    DATABASE_URL: Optional[str] = os.getenv("DATABASE_URL")

    # JWT Security Configuration
    JWT_SECRET_KEY: Optional[str] = os.getenv("JWT_SECRET_KEY")
    JWT_ALGORITHM: Optional[str] = os.getenv("JWT_ALGORITHM", "HS256")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = int(
        os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30")
    )
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = int(
        os.getenv("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "7")
    )
    PUBLIC_KEY: Optional[str] = os.getenv("PUBLIC_KEY")

    # Password Reset Configuration
    PASSWORD_RESET_TOKEN_EXPIRE_HOURS: int = int(
        os.getenv("PASSWORD_RESET_TOKEN_EXPIRE_HOURS", "1")
    )

    # Application Configuration
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    ALLOWED_ORIGINS: str = os.getenv("ALLOWED_ORIGINS", "")
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Fehadan Meat Processing Products Service"

    @property
    def allowed_origins_list(self) -> List[str]:
        """Convert ALLOWED_ORIGINS string to list"""
        if not self.ALLOWED_ORIGINS:
            return []
        return [
            origin.strip()
            for origin in self.ALLOWED_ORIGINS.split(",")
            if origin.strip()
        ]

    @property
    def async_database_url(self) -> str:
        """Convert DATABASE_URL to async format if needed"""
        if not self.DATABASE_URL:
            return "sqlite+aiosqlite:///./products.db"
        
        # Convert postgres:// to postgresql+asyncpg://
        if self.DATABASE_URL.startswith("postgres://"):
            return self.DATABASE_URL.replace("postgres://", "postgresql+asyncpg://", 1)
        elif self.DATABASE_URL.startswith("postgresql://"):
            return self.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://", 1)
        elif self.DATABASE_URL.startswith("sqlite://"):
            return self.DATABASE_URL.replace("sqlite://", "sqlite+aiosqlite://", 1)
        
        return self.DATABASE_URL

    # Redis Configuration
    REDIS_URL: Optional[str] = os.getenv("REDIS_URL")

    # Email Configuration
    POSTMARK_SERVER_TOKEN: Optional[str] = os.getenv("POSTMARK_SERVER_TOKEN")
    FROM_EMAIL: Optional[str] = os.getenv("FROM_EMAIL")
    EMAIL_ENABLED: bool = os.getenv("EMAIL_ENABLED", "false").lower() == "true"

    # Kafka Configuration
    KAFKA_BOOTSTRAP_SERVERS: Optional[str] = os.getenv("KAFKA_BOOTSTRAP_SERVERS")
    KAFKA_SECURITY_PROTOCOL: Optional[str] = os.getenv("KAFKA_SECURITY_PROTOCOL", "PLAINTEXT")
    KAFKA_API_KEY: Optional[str] = os.getenv("KAFKA_API_KEY")
    KAFKA_API_SECRET: Optional[str] = os.getenv("KAFKA_API_SECRET")
    KAFKA_SASL_MECHANISM: Optional[str] = os.getenv("KAFKA_SASL_MECHANISM", "PLAIN")
    KAFKA_SASL_USERNAME: Optional[str] = os.getenv("KAFKA_SASL_USERNAME")
    KAFKA_SASL_PASSWORD: Optional[str] = os.getenv("KAFKA_SASL_PASSWORD")
    KAFKA_SSL_CA_LOCATION: Optional[str] = os.getenv("KAFKA_SSL_CA_LOCATION")
    KAFKA_SSL_CERT_LOCATION: Optional[str] = os.getenv("KAFKA_SSL_CERT_LOCATION")
    KAFKA_SSL_KEY_LOCATION: Optional[str] = os.getenv("KAFKA_SSL_KEY_LOCATION")

    # Kafka Event System Configuration
    AUTH_CHANNEL: str = "auth"
    AUTHENTICATION_CHANNEL: str = "authentication"
    INVENTORY_CHANNEL: str = "inventory"
    ORDERS_CHANNEL: str = "orders"
    PRODUCTS_CHANNEL: str = "products"

    # Other services urls
    AUTH_SERVICE_URL: Optional[str] = os.getenv("AUTH_SERVICE_URL")
    INVENTORY_SERVICE_URL: Optional[str] = os.getenv("INVENTORY_SERVICE_URL")
    ORDER_SERVICE_URL: Optional[str] = os.getenv("ORDER_SERVICE_URL")
    NOTIFICATION_SERVICE_URL: Optional[str] = os.getenv("NOTIFICATION_SERVICE_URL")


class TestSettings(Settings):
    """Test-specific settings that override the base settings"""

    # Override environment
    ENVIRONMENT: str = "testing"

    # Use in-memory SQLite for testing (faster and isolated)
    DATABASE_URL: str = "sqlite+aiosqlite:///./test_products.db"

    # Override other settings for testing
    DEBUG: bool = True
    EMAIL_ENABLED: bool = False  # Disable emails during testing
    JWT_SECRET_KEY: str = "test_jwt_secret_key_for_testing_only"


# Create settings instance based on environment
def get_settings() -> Settings:
    """Get settings instance based on TESTING environment variable"""
    if os.getenv("TESTING", "false").lower() == "true":
        return TestSettings()
    return Settings()


# Global settings instance
settings = get_settings()
