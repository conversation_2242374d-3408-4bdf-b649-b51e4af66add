"""
Logging configuration and service for Products Service.

This module provides structured logging following the fehdan-inventory
service patterns.
"""

import logging
import os


def setup_logging():
    """Setup logging configuration for the application."""
    # Configure root logger
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
        ],
    )

    # Set specific log levels for different modules
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)


class LoggingService:
    """
    Centralized logging service for the Products Service.
    
    Provides structured logging with different levels and exception handling.
    """
    
    def __init__(self):
        self.environment = os.getenv("ENVIRONMENT", "dev")

    def log(self, message, level="info", exception=None, app_name="products"):
        """
        Logs a message with the specified level and optional exception details.

        Args:
            message (str): The log message to record.
            level (str, optional): The severity level of the log.
                Supported values are "info", "warning", "error", and "critical". Defaults to "info".
            exception (Exception, optional): An exception object to include in the log, if any. Defaults to None.
            app_name (str, optional): The name of the application or module generating the log. Defaults to "products".

        Raises:
            ValueError: If an unsupported log level is provided.
        """
        logger = logging.getLogger(app_name)

        if level == "info":
            logger.info(message)
        elif level == "warning":
            logger.warning(message)
        elif level == "error":
            if exception:
                logger.error(f"{message} - Exception: {str(exception)}", exc_info=True)
            else:
                logger.error(message)
        elif level == "critical":
            if exception:
                logger.critical(f"{message} - Exception: {str(exception)}", exc_info=True)
            else:
                logger.critical(message)
        else:
            raise ValueError(f"Unsupported log level: {level}")




