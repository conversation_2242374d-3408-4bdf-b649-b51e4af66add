services:
  # Products Service - Main application
  products_service:
    build:
      context: .
      dockerfile: Dockerfile
    image: fehdan-products-service:latest
    container_name: products_service
    ports:
      - "8002:8002"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_ALGORITHM=${JWT_ALGORITHM}
      - DEBUG=${DEBUG}
      - PROJECT_NAME=${PROJECT_NAME}
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - KAFKA_SECURITY_PROTOCOL=PLAINTEXT
      - AUTH_SERVICE_URL=${AUTH_SERVICE_URL}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS}
    env_file:
      - .env
    restart: always
    networks:
      - products_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # PostgreSQL Database
  postgres_db:
    image: postgres:15
    container_name: products_postgres_db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-defaultpassword}
      POSTGRES_DB: fehadan_products_db
    ports:
      - "5433:5432" # Different port to avoid conflicts
    volumes:
      - products_postgres_data:/var/lib/postgresql/data
    restart: always
    networks:
      - products_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7
    container_name: products_redis
    ports:
      - "6380:6379" # Different port to avoid conflicts
    restart: always
    networks:
      - products_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  # Zookeeper for Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: products_zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    restart: always
    networks:
      - products_network
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "2181"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Kafka Message Broker
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: products_kafka
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9093:9092" # Different port to avoid conflicts
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9093
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    restart: always
    networks:
      - products_network
    healthcheck:
      test:
        [
          "CMD",
          "kafka-topics",
          "--bootstrap-server",
          "localhost:29092",
          "--list",
        ]
      interval: 10s
      timeout: 10s
      retries: 5

networks:
  products_network:
    driver: bridge

volumes:
  products_postgres_data:
