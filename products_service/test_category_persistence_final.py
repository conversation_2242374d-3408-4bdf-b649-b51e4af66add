#!/usr/bin/env python3
"""
Final test script to verify category persistence fix.

This script directly tests the category persistence functionality
by calling the service methods directly, bypassing authentication.
"""

import asyncio
import sys
import os
from typing import List

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from app.core.database import async_session
from app.services.product import ProductService
from app.schemas import ProductUpdate


async def test_category_persistence():
    """Test that multiple categories are properly persisted during product updates."""
    print("🧪 Testing Category Persistence Fix")
    print("=" * 50)
    
    # Get database session
    async with async_session() as db:
        product_service = ProductService(db)
        
        # Test data
        product_id = 4
        test_categories = ["Meat", "Poultry", "Fresh", "Organic", "Premium", "Local"]
        
        print(f"📝 Testing product ID: {product_id}")
        print(f"📝 Test categories: {test_categories}")
        print()
        
        # Get current product state
        try:
            current_product = await product_service.get_product_by_id(product_id)
            print(f"✅ Current product found: {current_product.name}")
            print(f"📋 Current categories: {[cat.name for cat in current_product.categories]}")
            print()
        except Exception as e:
            print(f"❌ Failed to get current product: {e}")
            return False
        
        # Create update data with multiple categories
        update_data = ProductUpdate(
            name="Hen - Category Persistence Test",
            description="Testing multiple category persistence after all fixes",
            price="56.00",
            categories=test_categories
        )
        
        # Mock current_user for testing
        mock_user = {"id": 1, "email": "<EMAIL>", "role": "admin"}
        
        # Perform the update
        try:
            print("🔄 Updating product with multiple categories...")
            updated_product = await product_service.update_product(
                product_id, update_data, mock_user
            )
            
            print(f"✅ Product updated successfully!")
            print(f"📝 Updated name: {updated_product.name}")
            print(f"📝 Updated description: {updated_product.description}")
            print(f"💰 Updated price: {updated_product.price}")
            print()
            
            # Check categories
            updated_categories = [cat.name for cat in updated_product.categories]
            print(f"📋 Updated categories ({len(updated_categories)}): {updated_categories}")
            print()
            
            # Verify all categories were saved
            missing_categories = set(test_categories) - set(updated_categories)
            extra_categories = set(updated_categories) - set(test_categories)
            
            if not missing_categories and not extra_categories:
                print("🎉 SUCCESS: All categories were properly persisted!")
                print(f"✅ Expected {len(test_categories)} categories, got {len(updated_categories)}")
                print("✅ All expected categories are present")
                print("✅ No unexpected categories found")
                return True
            else:
                print("❌ FAILURE: Category persistence issue detected!")
                if missing_categories:
                    print(f"❌ Missing categories: {missing_categories}")
                if extra_categories:
                    print(f"❌ Extra categories: {extra_categories}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to update product: {e}")
            import traceback
            traceback.print_exc()
            return False


async def main():
    """Main test function."""
    print("🚀 Starting Category Persistence Test")
    print("=" * 50)
    
    try:
        success = await test_category_persistence()
        
        print()
        print("=" * 50)
        if success:
            print("🎉 OVERALL RESULT: CATEGORY PERSISTENCE FIX VERIFIED!")
            print("✅ Multiple categories are properly saved during product updates")
            print("✅ The original bug has been successfully resolved")
        else:
            print("❌ OVERALL RESULT: CATEGORY PERSISTENCE ISSUE STILL EXISTS!")
            print("❌ The fix needs further investigation")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
