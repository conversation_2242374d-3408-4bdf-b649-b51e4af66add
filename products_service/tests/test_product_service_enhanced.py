"""
Test suite for enhanced ProductService with CategoryRepository integration.
"""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from decimal import Decimal
from fastapi import HTTPException

from app.services.product import ProductService
from app.schemas import ProductCreate, ProductUpdate


@pytest.fixture
def product_service(test_db: AsyncSession) -> ProductService:
    return ProductService(test_db)


@pytest.mark.asyncio
async def test_validate_and_process_categories(product_service: ProductService):
    """Test category validation and processing."""
    # Test valid categories
    valid_categories = ["meat", "beef", "premium"]
    result = await product_service._validate_and_process_categories(valid_categories)
    assert result == ["meat", "beef", "premium"]
    
    # Test empty and None categories
    result = await product_service._validate_and_process_categories([])
    assert result == []
    
    result = await product_service._validate_and_process_categories(None)
    assert result == []
    
    # Test categories with whitespace
    categories_with_whitespace = ["  meat  ", "beef", "  ", "premium"]
    result = await product_service._validate_and_process_categories(categories_with_whitespace)
    assert result == ["meat", "beef", "premium"]
    
    # Test category name too long
    with pytest.raises(HTTPException) as exc_info:
        long_name = "a" * 101  # Assuming max length is 100
        await product_service._validate_and_process_categories([long_name])
    assert exc_info.value.status_code == 400


@pytest.mark.asyncio
async def test_ensure_categories_exist(product_service: ProductService):
    """Test ensuring categories exist in database."""
    category_names = ["meat", "beef", "premium"]
    categories = await product_service._ensure_categories_exist(category_names)
    
    assert len(categories) == 3
    assert all(cat.name in category_names for cat in categories)
    assert all(cat.id is not None for cat in categories)


@pytest.mark.asyncio
async def test_create_product_with_validation(product_service: ProductService):
    """Test product creation with enhanced validation."""
    product_data = ProductCreate(
        name="Enhanced Test Product",
        description="A product for testing enhanced service",
        price=Decimal("19.99"),
        currency="USD",
        in_stock=True,
        stock_quantity=100,
        categories=["meat", "beef", "premium"]
    )

    # Mock current_user for testing
    current_user = {"id": 1, "email": "<EMAIL>"}
    
    response = await product_service.create_product(current_user, product_data)
    assert response.name == product_data.name
    assert response.price == product_data.price
    assert response.id is not None
    assert len(response.categories) == 3
    
    # Check that categories were created and assigned
    category_names = [cat.name for cat in response.categories]
    assert "meat" in category_names
    assert "beef" in category_names
    assert "premium" in category_names


@pytest.mark.asyncio
async def test_validate_product_data(product_service: ProductService):
    """Test product data validation."""
    # Test valid product data
    valid_product = ProductCreate(
        name="Valid Product",
        description="A valid product",
        price=Decimal("19.99"),
        categories=["meat", "beef"]
    )
    
    # Should not raise any exception
    await product_service.validate_product_data(valid_product)
    
    # Test invalid product data - empty name
    with pytest.raises(HTTPException) as exc_info:
        invalid_product = ProductCreate(
            name="",
            description="Invalid product",
            price=Decimal("19.99")
        )
        await product_service.validate_product_data(invalid_product)
    assert exc_info.value.status_code == 400
    
    # Test invalid product data - negative price
    with pytest.raises(HTTPException) as exc_info:
        invalid_product = ProductCreate(
            name="Invalid Product",
            description="Invalid product",
            price=Decimal("-10.00")
        )
        await product_service.validate_product_data(invalid_product)
    assert exc_info.value.status_code == 400
    
    # Test duplicate categories
    with pytest.raises(HTTPException) as exc_info:
        invalid_product = ProductCreate(
            name="Invalid Product",
            description="Invalid product",
            price=Decimal("19.99"),
            categories=["meat", "beef", "meat"]  # Duplicate "meat"
        )
        await product_service.validate_product_data(invalid_product)
    assert exc_info.value.status_code == 400


@pytest.mark.asyncio
async def test_create_category_service(product_service: ProductService):
    """Test category creation through service."""
    category = await product_service.create_category("test_service_category", "A test category")
    assert category.name == "test_service_category"
    assert category.description == "A test category"
    assert category.is_active is True
    assert category.id is not None
    
    # Test creating category with empty name
    with pytest.raises(HTTPException) as exc_info:
        await product_service.create_category("", "Empty name category")
    assert exc_info.value.status_code == 400


@pytest.mark.asyncio
async def test_get_category_by_name_service(product_service: ProductService):
    """Test getting category by name through service."""
    # Create a category first
    await product_service.create_category("test_get_category", "A test category")
    
    # Get the category
    category = await product_service.get_category_by_name("test_get_category")
    assert category is not None
    assert category.name == "test_get_category"
    
    # Test getting non-existent category
    category = await product_service.get_category_by_name("non_existent")
    assert category is None
    
    # Test getting category with empty name
    category = await product_service.get_category_by_name("")
    assert category is None


@pytest.mark.asyncio
async def test_get_categories_service(product_service: ProductService):
    """Test getting categories through service."""
    # Create some categories
    await product_service.create_category("category1", "First category")
    await product_service.create_category("category2", "Second category")
    
    # Get active categories
    categories = await product_service.get_categories(active_only=True)
    assert len(categories) >= 2
    assert "category1" in categories
    assert "category2" in categories


@pytest.mark.asyncio
async def test_search_products_enhanced(product_service: ProductService):
    """Test enhanced product search functionality."""
    # Create products with categories for testing
    product1_data = ProductCreate(
        name="Beef Steak",
        description="Premium beef steak",
        price=Decimal("25.99"),
        categories=["meat", "beef", "premium"]
    )
    
    product2_data = ProductCreate(
        name="Chicken Breast",
        description="Fresh chicken breast",
        price=Decimal("15.99"),
        categories=["meat", "chicken", "fresh"]
    )
    
    current_user = {"id": 1, "email": "<EMAIL>"}
    
    await product_service.create_product(current_user, product1_data)
    await product_service.create_product(current_user, product2_data)
    
    # Search by product name
    results = await product_service.search_products("beef")
    assert len(results) >= 1
    assert any("beef" in product.name.lower() for product in results)
    
    # Search by category
    results = await product_service.search_products("chicken")
    assert len(results) >= 1
    
    # Search with empty query
    results = await product_service.search_products("")
    assert len(results) == 0


@pytest.mark.asyncio
async def test_list_products_with_category_filter(product_service: ProductService):
    """Test product listing with category filtering."""
    # Create products with different categories
    beef_product = ProductCreate(
        name="Beef Product",
        description="Beef product",
        price=Decimal("20.99"),
        categories=["meat", "beef"]
    )
    
    chicken_product = ProductCreate(
        name="Chicken Product",
        description="Chicken product",
        price=Decimal("15.99"),
        categories=["meat", "chicken"]
    )
    
    current_user = {"id": 1, "email": "<EMAIL>"}
    
    await product_service.create_product(current_user, beef_product)
    await product_service.create_product(current_user, chicken_product)
    
    # Filter by beef category
    beef_products = await product_service.list_products(
        filter={"category": "beef"}
    )
    assert len(beef_products) >= 1
    assert all(any(cat.name == "beef" for cat in product.categories) for product in beef_products)
    
    # Filter by non-existent category
    no_products = await product_service.list_products(
        filter={"category": "non_existent_category"}
    )
    assert len(no_products) == 0
