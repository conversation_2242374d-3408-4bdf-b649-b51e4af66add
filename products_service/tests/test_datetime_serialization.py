"""
Test datetime serialization in event publishing.

This module tests the fix for JSON serialization errors when datetime objects
are included in event data.
"""

import pytest
import json
from datetime import datetime
from decimal import Decimal
from unittest.mock import AsyncMock, patch

from app.events.publisher import EventPublisher, DateTimeEncoder
from app.events.schemas import ProductUpdatedEvent
from app.services.product import ProductService
from app.schemas import ProductResponse, ProductUpdate


class TestDateTimeSerialization:
    """Test datetime serialization in events."""

    def test_datetime_encoder_handles_datetime(self):
        """Test that DateTimeEncoder properly converts datetime to ISO format."""
        encoder = DateTimeEncoder()
        test_datetime = datetime(2023, 12, 25, 10, 30, 45)
        
        # Test direct encoding
        result = encoder.default(test_datetime)
        assert result == "2023-12-25T10:30:45"
        
        # Test JSON dumps with encoder
        data = {
            "timestamp": test_datetime,
            "name": "Test Product"
        }
        json_str = json.dumps(data, cls=DateTimeEncoder)
        parsed = json.loads(json_str)
        assert parsed["timestamp"] == "2023-12-25T10:30:45"
        assert parsed["name"] == "Test Product"

    def test_datetime_encoder_handles_decimal(self):
        """Test that DateTimeEncoder properly converts Decimal to float."""
        encoder = DateTimeEncoder()
        test_decimal = Decimal("19.99")
        
        # Test direct encoding
        result = encoder.default(test_decimal)
        assert result == 19.99
        assert isinstance(result, float)
        
        # Test JSON dumps with encoder
        data = {
            "price": test_decimal,
            "name": "Test Product"
        }
        json_str = json.dumps(data, cls=DateTimeEncoder)
        parsed = json.loads(json_str)
        assert parsed["price"] == 19.99
        assert parsed["name"] == "Test Product"

    def test_datetime_encoder_handles_mixed_types(self):
        """Test that DateTimeEncoder handles datetime, Decimal, and regular types."""
        test_data = {
            "id": 123,
            "name": "Test Product",
            "price": Decimal("29.99"),
            "created_at": datetime(2023, 12, 25, 10, 30, 45),
            "updated_at": datetime(2023, 12, 26, 15, 45, 30),
            "in_stock": True,
            "tags": ["meat", "premium"]
        }
        
        json_str = json.dumps(test_data, cls=DateTimeEncoder)
        parsed = json.loads(json_str)
        
        assert parsed["id"] == 123
        assert parsed["name"] == "Test Product"
        assert parsed["price"] == 29.99
        assert parsed["created_at"] == "2023-12-25T10:30:45"
        assert parsed["updated_at"] == "2023-12-26T15:45:30"
        assert parsed["in_stock"] is True
        assert parsed["tags"] == ["meat", "premium"]

    @pytest.mark.asyncio
    async def test_event_publisher_serializes_datetime_in_events(self):
        """Test that EventPublisher can serialize events with datetime fields."""
        # Mock the Kafka producer
        with patch('app.events.publisher.create_producer') as mock_create_producer:
            mock_producer = AsyncMock()
            mock_create_producer.return_value = mock_producer
            
            publisher = EventPublisher()
            
            # Create event with datetime in data
            event_data = {
                "product_id": 123,
                "name": "Test Product",
                "updated_fields": {
                    "created_at": {
                        "old_value": datetime(2023, 12, 25, 10, 30, 45),
                        "new_value": datetime(2023, 12, 26, 15, 45, 30)
                    },
                    "price": {
                        "old_value": Decimal("19.99"),
                        "new_value": Decimal("29.99")
                    }
                },
                "updated_by": "<EMAIL>"
            }
            
            event = ProductUpdatedEvent.create(
                product_id=123,
                product_name="Test Product",
                updated_fields=event_data["updated_fields"],
                updated_by="<EMAIL>"
            )
            
            # This should not raise a JSON serialization error
            await publisher.publish_event(event)
            
            # Verify producer.produce was called
            mock_producer.produce.assert_called_once()
            
            # Verify the message value is valid JSON
            call_args = mock_producer.produce.call_args
            message_value = call_args[1]['value']  # Get the 'value' keyword argument
            
            # Should be able to parse the JSON without errors
            parsed_data = json.loads(message_value)
            assert parsed_data is not None
            assert "data" in parsed_data
            assert "updated_fields" in parsed_data["data"]

    def test_get_updated_fields_converts_datetime_to_iso(self):
        """Test that _get_updated_fields converts datetime objects to ISO strings."""
        # Create mock ProductResponse with datetime fields
        original_product = ProductResponse(
            id=123,
            name="Original Product",
            description="Original description",
            price=Decimal("19.99"),
            currency="USD",
            in_stock=True,
            stock_quantity=10,
            unity_of_measure="lb",
            categories=[],
            tags=[],
            images=[],
            created_at=datetime(2023, 12, 25, 10, 30, 45),
            updated_at=datetime(2023, 12, 25, 10, 30, 45)
        )
        
        # Create update data that would trigger datetime comparison
        update_data = ProductUpdate(
            name="Updated Product",
            price=Decimal("29.99")
        )
        
        # Create a ProductService instance (we'll test the method directly)
        service = ProductService(db=None)  # db not needed for this test
        
        # Call the method
        updated_fields = service._get_updated_fields(original_product, update_data)
        
        # Verify that the result can be JSON serialized
        json_str = json.dumps(updated_fields)
        parsed = json.loads(json_str)
        
        # Verify structure
        assert "name" in parsed
        assert "price" in parsed
        assert parsed["name"]["old_value"] == "Original Product"
        assert parsed["name"]["new_value"] == "Updated Product"
        assert parsed["price"]["old_value"] == 19.99
        assert parsed["price"]["new_value"] == 29.99
        
        # If datetime fields were included, they should be ISO strings
        for field_data in parsed.values():
            if "old_value" in field_data:
                old_val = field_data["old_value"]
                new_val = field_data["new_value"]
                # If these were datetime objects, they would cause JSON serialization to fail
                # The fact that we can parse the JSON means they were properly converted
                assert isinstance(old_val, (str, int, float, bool, type(None)))
                assert isinstance(new_val, (str, int, float, bool, type(None)))
