#!/usr/bin/env python3
"""
Verification script to confirm the products service is completely self-contained.
This script checks that all services are running independently within the products_service directory.
"""

import subprocess
import sys
import time
import requests
import json

def run_command(command, cwd=None):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            cwd=cwd
        )
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_containers():
    """Check that all required containers are running."""
    print("🔍 Checking container status...")
    
    success, stdout, stderr = run_command("docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'")
    if not success:
        print(f"❌ Failed to check containers: {stderr}")
        return False
    
    required_containers = [
        "products_service",
        "products_postgres_db", 
        "products_redis",
        "products_kafka",
        "products_zookeeper"
    ]
    
    running_containers = stdout
    print(f"📋 Running containers:\n{running_containers}")
    
    for container in required_containers:
        if container not in running_containers:
            print(f"❌ Missing container: {container}")
            return False
        if "healthy" not in running_containers or "Up" not in running_containers:
            print(f"⚠️  Container {container} may not be healthy")
    
    print("✅ All required containers are running")
    return True

def check_api_health():
    """Check that the API is responding correctly."""
    print("\n🔍 Checking API health...")
    
    try:
        response = requests.get("http://localhost:8002/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ API Health Check: {json.dumps(health_data, indent=2)}")
            
            # Verify database connection
            if health_data.get("database") == "connected":
                print("✅ Database connection confirmed")
            else:
                print("❌ Database connection issue")
                return False
                
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API health check failed: {e}")
        return False

def check_products_endpoint():
    """Check that the products endpoint is working."""
    print("\n🔍 Checking products endpoint...")
    
    try:
        response = requests.get("http://localhost:8002/api/v1/products/", timeout=10)
        if response.status_code == 200:
            products = response.json()
            print(f"✅ Products endpoint working: {len(products)} products found")
            return True
        else:
            print(f"❌ Products endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Products endpoint failed: {e}")
        return False

def check_port_isolation():
    """Check that services are using isolated ports."""
    print("\n🔍 Checking port isolation...")
    
    expected_ports = {
        "8002": "Products Service API",
        "5433": "PostgreSQL Database", 
        "6380": "Redis Cache",
        "9093": "Kafka External"
    }
    
    success, stdout, stderr = run_command("docker ps --format 'table {{.Names}}\t{{.Ports}}'")
    if not success:
        print(f"❌ Failed to check ports: {stderr}")
        return False
    
    print("📋 Port mappings:")
    for port, service in expected_ports.items():
        if f":{port}->" in stdout:
            print(f"✅ {service}: Port {port} correctly mapped")
        else:
            print(f"❌ {service}: Port {port} not found")
            return False
    
    return True

def main():
    """Main verification function."""
    print("🚀 Products Service Self-Contained Verification")
    print("=" * 60)
    
    checks = [
        ("Container Status", check_containers),
        ("API Health", check_api_health), 
        ("Products Endpoint", check_products_endpoint),
        ("Port Isolation", check_port_isolation)
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        print(f"\n📋 Running {check_name} check...")
        if not check_func():
            all_passed = False
            print(f"❌ {check_name} check FAILED")
        else:
            print(f"✅ {check_name} check PASSED")
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 SUCCESS: Products service is completely self-contained!")
        print("✅ All infrastructure is running independently")
        print("✅ No external dependencies detected")
        print("✅ Service is ready for independent deployment")
    else:
        print("❌ FAILURE: Some checks failed")
        print("🔧 Please review the issues above")
        sys.exit(1)

if __name__ == "__main__":
    main()
